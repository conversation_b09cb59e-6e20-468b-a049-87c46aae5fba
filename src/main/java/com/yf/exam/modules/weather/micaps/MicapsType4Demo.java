package com.yf.exam.modules.weather.micaps;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

/**
 * MICAPS第四类数据解析演示类
 * 展示如何使用改进后的parseType4MdfsData方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Component
public class MicapsType4Demo {

    @Autowired
    private MicapsDataService micapsDataService;

    @Autowired
    private MdfsBinaryParser binaryParser;

    /**
     * 演示第四类MDFS数据解析
     * 
     * @param filePath MDFS文件路径
     */
    public void demonstrateType4Parsing(String filePath) {
        log.info("开始演示第四类MDFS数据解析...");
        
        try {
            // 使用MicapsDataService解析文件
            MicapsData data = micapsDataService.parseMicapsFile(filePath);
            
            if (data instanceof MicapsType4Data) {
                MicapsType4Data type4Data = (MicapsType4Data) data;
                
                log.info("=== 第四类数据解析结果 ===");
                log.info("数据类型: {}", type4Data.getDataType());
                log.info("描述: {}", type4Data.getDescription());
                log.info("时间: {}-{:02d}-{:02d} {:02d}:00", 
                    type4Data.getYear(), type4Data.getMonth(), 
                    type4Data.getDay(), type4Data.getHour());
                log.info("预报时效: {} 小时", type4Data.getForecastHour());
                log.info("层次: {}", type4Data.getLevel());
                
                log.info("=== 格点信息 ===");
                log.info("经度范围: {:.1f}° - {:.1f}°", type4Data.getStartLon(), type4Data.getEndLon());
                log.info("纬度范围: {:.1f}° - {:.1f}°", type4Data.getStartLat(), type4Data.getEndLat());
                log.info("格点间距: 经度 {:.2f}°, 纬度 {:.2f}°", 
                    type4Data.getLonInterval(), type4Data.getLatInterval());
                log.info("格点数量: {}×{} = {} 个格点", 
                    type4Data.getLonGridNum(), type4Data.getLatGridNum(),
                    type4Data.getLonGridNum() * type4Data.getLatGridNum());
                
                if (type4Data.getGridValues() != null && !type4Data.getGridValues().isEmpty()) {
                    log.info("实际格点数据: {} 个", type4Data.getGridValues().size());

                    // 显示统计信息
                    showGridStatistics(type4Data);

                    // 显示部分格点数据
                    showSampleGridData(type4Data);

                    // 演示位置查询
                    demonstratePositionQuery(type4Data);
                } else {
                    log.warn("格点数据为空");
                }
                
            } else {
                log.warn("解析结果不是第四类数据，实际类型: {}", data.getClass().getSimpleName());
            }
            
        } catch (IOException e) {
            log.error("解析文件失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("处理过程中出现异常", e);
        }
    }

    /**
     * 显示格点数据统计信息
     */
    private void showGridStatistics(MicapsType4Data data) {
        try {
            if (data.getGridValues() != null && !data.getGridValues().isEmpty()) {
                log.info("=== 格点数据统计 ===");

                // 手动计算统计信息
                double min = data.getGridValues().stream().mapToDouble(Double::doubleValue).min().orElse(0);
                double max = data.getGridValues().stream().mapToDouble(Double::doubleValue).max().orElse(0);
                double avg = data.getGridValues().stream().mapToDouble(Double::doubleValue).average().orElse(0);

                log.info("最小值: {:.2f}", min);
                log.info("最大值: {:.2f}", max);
                log.info("平均值: {:.2f}", avg);
                log.info("有效格点数: {}", data.getGridValues().size());
            }
        } catch (Exception e) {
            log.debug("获取统计信息失败: {}", e.getMessage());
        }
    }

    /**
     * 显示部分格点数据样本
     */
    private void showSampleGridData(MicapsType4Data data) {
        log.info("=== 格点数据样本 ===");
        
        // 显示前10个格点数据
        for (int i = 0; i < Math.min(10, data.getGridValues().size()); i++) {
            Double value = data.getGridValues().get(i);
            log.info("格点 {}: {:.2f}", i + 1, value);
        }
        
        // 显示特定位置的格点数据
        if (data.getLonGridNum() > 10 && data.getLatGridNum() > 10) {
            Double centerValue = data.getGridValue(data.getLatGridNum() / 2, data.getLonGridNum() / 2);
            if (centerValue != null) {
                log.info("中心位置格点值: {:.2f}", centerValue);
            }
        }
    }

    /**
     * 演示位置查询功能
     */
    private void demonstratePositionQuery(MicapsType4Data data) {
        log.info("=== 位置查询演示 ===");

        try {
            // 测试几个位置的数值查询
            double[] testLons = {105.0, 110.0, 115.0};
            double[] testLats = {35.0, 40.0, 45.0};

            for (int i = 0; i < testLons.length; i++) {
                Double value = micapsDataService.getGridValueAtPosition(data, testLons[i], testLats[i]);
                if (value != null) {
                    log.info("位置({:.1f}°E, {:.1f}°N)的数值: {:.2f}", testLons[i], testLats[i], value);
                } else {
                    log.info("位置({:.1f}°E, {:.1f}°N)超出范围或无数据", testLons[i], testLats[i]);
                }
            }
        } catch (Exception e) {
            log.debug("位置查询失败: {}", e.getMessage());
        }
    }

    /**
     * 直接使用二进制解析器演示
     */
    public void demonstrateBinaryParser(String filePath) {
        log.info("开始演示二进制解析器...");
        
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("文件不存在: {}", filePath);
                return;
            }
            
            // 创建文件头
            MdfsFileHeader header = new MdfsFileHeader();
            header.setDataType(4);
            header.setDescription("第四类演示数据");
            
            // 直接调用二进制解析器
            MicapsType4Data result = binaryParser.parseType4BinaryData(file, header);
            
            log.info("二进制解析器结果:");
            log.info("数据类型: {}", result.getDataType());
            log.info("描述: {}", result.getDescription());
            log.info("格点数据大小: {}", result.getGridValues() != null ? result.getGridValues().size() : 0);
            
        } catch (Exception e) {
            log.error("二进制解析器演示失败", e);
        }
    }

    /**
     * 演示方法使用说明
     */
    public void showUsageExample() {
        log.info("=== MICAPS第四类数据解析使用示例 ===");
        log.info("1. 使用MicapsDataService解析:");
        log.info("   MicapsData data = micapsDataService.parseMicapsFile(\"path/to/type4.mdfs\");");
        log.info("   if (data instanceof MicapsType4Data) {");
        log.info("       MicapsType4Data gridData = (MicapsType4Data) data;");
        log.info("       // 处理格点数据");
        log.info("   }");
        log.info("");
        log.info("2. 直接使用二进制解析器:");
        log.info("   MdfsFileHeader header = new MdfsFileHeader();");
        log.info("   header.setDataType(4);");
        log.info("   MicapsType4Data result = binaryParser.parseType4BinaryData(file, header);");
        log.info("");
        log.info("3. 获取格点数据:");
        log.info("   List<Double> gridValues = gridData.getGridValues();");
        log.info("   Double pointValue = gridData.getGridValue(latIdx, lonIdx);");
        log.info("");
        log.info("4. 位置查询:");
        log.info("   Double value = micapsDataService.getGridValueAtPosition(data, 105.0, 35.0);");
    }
}
